name: pre-commit

on:
  pull_request:
    branches:
      - "16.0*"
  push:
    branches:
      - "16.0"
      - "16.0-ocabot-*"

jobs:
  pre-commit:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v2
        with:
          python-version: "3.11"
      - name: Get python version
        run: echo "PY=$(python -VV | sha256sum | cut -d' ' -f1)" >> $GITHUB_ENV
      - uses: actions/cache@v1
        with:
          path: ~/.cache/pre-commit
          key: pre-commit|${{ env.PY }}|${{ hashFiles('.pre-commit-config.yaml') }}
      - name: Install pre-commit
        run: pip install pre-commit
      - name: Run pre-commit
        run: pre-commit run --all-files --show-diff-on-failure --color=always
        env:
          # Consider valid a PR that changes README fragments but doesn't
          # change the README.rst file itself. It's not really a problem
          # because the bot will update it anyway after merge. This way, we
          # lower the barrier for functional contributors that want to fix the
          # readme fragments, while still letting developers get README
          # auto-generated (which also helps functionals when using runboat).
          # DOCS https://pre-commit.com/#temporarily-disabling-hooks
          SKIP: oca-gen-addon-readme
      - name: Check that all files generated by pre-commit are in git
        run: |
          newfiles="$(git ls-files --others --exclude-from=.gitignore)"
          if [ "$newfiles" != "" ] ; then
              echo "Please check-in the following files:"
              echo "$newfiles"
              exit 1
          fi
