<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <template id="report_invoice_custom" name="Invoice Kwintasi">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                 <t t-foreach="docs" t-as="doc">

                    <div class="page" style="font-size: 15px;">
                        <table class="table" style="border: none; width: 100%; border-collapse: collapse;">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 35%; padding: 5px; border: none;">
                                        <table style="border: none !important; width: 100%; border-collapse: separate; border-spacing: 0;">
                                            <tr t-att-style="style_no_border">
                                                <td style="width: 25%; border: none;"><strong>Nomor</strong></td>
                                                <td style="width: 2%; border: none;"><strong>: </strong></td>
                                                <td style="width: 73%; border: none;"> <span t-field="doc.name"/></td>
                                            </tr>
                                            <tr t-att-style="style_no_border">
                                                <td style="width: 25%; border: none;"><strong>Perihal</strong></td>
                                                <td style="width: 2%; border: none;"><strong>: </strong></td>
                                                <td style="width: 73%; border: none;"><span> Permintaan Pembayaran</span></td>
                                            </tr>
                                            <tr t-att-style="style_no_border">
                                                <td style="width: 25%; border: none;"><strong>Lamp</strong></td>
                                                <td style="width: 2%; border: none;"><strong>: </strong></td>
                                                <td style="width: 73%; border: none;"><span> 1 Berkas</span></td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td style="width: 40%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </td>
                                    <td style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <t t-set="invoice_date" t-value="doc.invoice_date and doc.invoice_date.strftime('%-d %B %Y') or '-'"/>
                                        <t t-set="company_street" t-value="doc.company_id.city"/>
                                        <strong><t t-esc="company_street"/>, <t t-esc="invoice_date"/></strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table" style="border: none; width: 100%; border-collapse: collapse;">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <strong></strong>
                                    </td>
                                    <td style="width: 50%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </td>
                                    <td style="width: 25%; padding: 5px; border: none; text-align: left;">
                                        <div style="border: none;"><strong>Kepada : </strong></div>
                                        <div style="border: none;"><span t-field="doc.partner_id"/></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>



                        <t t-set="style_no_border" t-value="'border: none !important; padding: 0px; margin: 0px;'"/>
                        <t t-set="style_table_left" t-value="'width: 30%; border: none; padding: 0px 0px 0px 5px; margin: 0px;'"/>
                        <t t-set="style_table_center" t-value="'width: 2%; border: none; padding: 0px 0px 0px 5px; margin: 0px;'"/>
                        <t t-set="style_table_right" t-value="'width: 68%; border: none; padding: 0px 0px 0px 5px; margin: 0px;'"/>

                        <div>Menujuk Surat Perjanjian /SPB/SPBL/SPK/SPKL/SPMP/SPPB Antara <span t-field="doc.partner_id" /> dengan <span t-field="doc.company_id.name"/>, sebagai berikut:</div>

                        <table class="table" style="border: none; width: 100%; border-collapse: collapse;">
                            <tbody style="border: none; ">
                                <tr t-att-style="style_no_border" >
                                    <td t-att-style="style_table_left"><span>No Kontrak</span></td>
                                    <td t-att-style="style_table_center"><span>:</span></td>
                                    <td t-att-style="style_table_right">
                                        <t t-if="doc.analytic_distribution">
                                            <t t-foreach="doc.analytic_distribution.items()" t-as="analytic">
                                                <t t-set="analytic_name" t-value="env['account.analytic.account'].browse(int(analytic[0])).name"/>
                                                <span t-esc="analytic_name"/>
                                            </t>
                                        </t>
                                    </td>
                                </tr>
                                <tr t-att-style="style_no_border" >
                                    <td t-att-style="style_table_left"><span>Tanggal</span></td>
                                    <td t-att-style="style_table_center"><span>:</span></td>
                                    <td t-att-style="style_table_right">
                                        <t t-set="invoice_date" t-value="doc.invoice_date and doc.invoice_date.strftime('%-d %B %Y') or '-'"/>
                                        <span><t t-esc="invoice_date"/></span>
                                    </td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td t-att-style="style_table_left"><span>Nama Pengadaan/Pekerjaan</span></td>
                                    <td t-att-style="style_table_center"><span>:</span></td>
                                    <td t-att-style="style_table_right">
                                        <t t-foreach="doc.line_ids" t-as="line">
                                            <strong t-if="line.display_type == 'product'">
                                                <t t-esc="line.name"/>
                                            </strong>
                                        </t>
                                    </td>
                                </tr>
                                <tr t-att-style="style_no_border" >
                                    <td t-att-style="style_table_left"><span>Nilai Jasa</span></td>
                                    <td t-att-style="style_table_center"><span>:</span></td>
                                    <td t-att-style="style_table_right"><span t-esc="'Rp {:,.2f}'.format(doc.amount_total)"/></td>
                                </tr>
                                <tr t-att-style="style_no_border" >
                                    <td t-att-style="style_table_left"><span></span></td>
                                    <td t-att-style="style_table_center"><span></span></td>
                                    <td t-att-style="style_table_right "><span t-esc="doc.amount_to_text_id(doc.amount_total)"/> <span t-if="doc.amount_tax"> sudah termasuk <t t-esc="doc.line_ids.tax_ids.name"/> </span></td>
                                </tr>
                            </tbody>
                        </table>

                        <div>Dari nilai kontrak tersebut, jumlah tersebut hendaknya dipindahkan bukan kerekening kami:</div>

                        <table class="table" style="border: none; width: 100%; border-collapse: collapse; separate;">
                            <tbody style="border: none;">
                                <tr t-att-style="style_no_border">
                                    <td t-att-style="style_table_left"><span>No Rekening</span></td>
                                    <td t-att-style="style_table_center"><span>:</span></td>
                                    <td t-att-style="style_table_right"><span t-field="doc.partner_bank_id.acc_number"/></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td t-att-style="style_table_left"><span>Atas Nama</span></td>
                                    <td t-att-style="style_table_center"><span>:</span></td>
                                    <td t-att-style="style_table_right"><span t-field="doc.partner_bank_id.partner_id"/></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td t-att-style="style_table_left"><span>Nama Bank</span></td>
                                    <td t-att-style="style_table_center"><span>:</span></td>
                                    <td t-att-style="style_table_right"><span t-field="doc.partner_bank_id.bank_id"/></td>
                                </tr>
                            </tbody>
                        </table>

                        <div>Setelah pembayaran dimaksud dilaksanakan , maka seluruh pembayaran dalam rangka Surat Perjanjian diatas yang telah kami terima adalah sebesar
                            <span t-esc="'Rp {:,.2f}'.format(doc.amount_total) "/> ( <span t-esc="doc.amount_to_text_id(doc.amount_total)"/> ) <span t-if="doc.amount_tax"> sudah termasuk <t t-esc="doc.line_ids.tax_ids.name"/></span>.
                        </div>
                        <br/>
                        <div>Sebagai Kelengkapan dan surat permintaan pembayaran ini kami sampaikan:</div>

                        <table class="table" style="border: none; width: 100%; border-collapse: collapse; separate;">
                            <tbody style="border: none;">
                                <tr t-att-style="style_no_border">
                                    <td style="width: 1%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>1.</span></td>
                                    <td style="width: 99%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>Kuitansi bermaterai</span></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 1%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>2.</span></td>
                                    <td style="width: 99; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>Invoice</span></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 1%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>3.</span></td>
                                    <td style="width: 99%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>Fotocopy SPKP dan NPWP</span></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 1%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>4.</span></td>
                                    <td style="width: 99%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>Faktur Pajak</span></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 1%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>5.</span></td>
                                    <td style="width: 99%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>Copy Surat Perjanjian Kontrak Nomor
                                        <!-- <t t-foreach="doc.analytic_distribution.items()" t-as="analytic">
                                            <t t-set="analytic_name" t-value="env['account.analytic.account'].browse(int(analytic[0])).name"/>
                                            <span t-esc="analytic_name"/>
                                        </t> -->
                                        <t t-if="doc.analytic_distribution">
                                            <t t-foreach="doc.analytic_distribution.items()" t-as="analytic">
                                                <t t-set="analytic_name" t-value="env['account.analytic.account'].browse(int(analytic[0])).name"/>
                                                <span t-esc="analytic_name"/>
                                            </t>
                                        </t>
                                    , Tanggal <t t-set="invoice_date" t-value="doc.invoice_date and doc.invoice_date.strftime('%-d %B %Y') or '-'"/><span><t t-esc="invoice_date"/></span></span></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 1%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>6.</span></td>
                                    <td style="width: 99%; border: none; padding: 0px 0px 0px 5px; margin: 0px;"><span>Berita Acara Penyerahan pekerjaan dan Berita Acara Pemeriksaan selesai pekerjaan atau Berita Acara Pemeriksaan Barang/Spare Part (TUG4) an Bon Penerimaan Barang/Spare Part </span></td>
                                </tr>

                            </tbody>
                        </table>

                        <table style="width: 100%; border: none; ">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 50%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <t t-esc="doc.company_id.name"/>
                                    </th>
                                </tr>
                            </tbody>
                        </table>

                        <br/><br/><br/><br/><br/>

                        <table style="width: 100%; border: none; ">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <th style="width: 75%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center; text-decoration: underline;">
                                        <t t-esc="doc.approver and str(doc.approver).upper() or ''"/>
                                    </th>
                                </tr>
                            </tbody>

                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 75%; padding: 5px; border: none; text-align: center;">
                                        <strong><t/></strong>
                                    </td>
                                    <td style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <strong><t t-esc="doc.approver_role and str(doc.approver_role).capitalize() or ''"/></strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </div>


                    <p style="page-break-before:always;"> </p>


                    <div class="page">
                        <h2 style="text-align: center; text-decoration: underline;">INVOICE</h2>
                        <table class="table" style="border: none; width: 50%; border-collapse: collapse;">
                            <tbody style="border: none;">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 35%; border: none;"><strong>No</strong></td>
                                    <td style="width: 2%; border: none;"><strong>:</strong></td>
                                    <td style="width: 53%; border: none;"><span t-field="doc.name"/></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 35%; border: none;"><strong>Tanggal</strong></td>
                                    <td style="width: 2%; border: none;"><strong>:</strong></td>
                                    <td style="width: 53%; border: none;">
                                        <t t-set="invoice_date" t-value="doc.invoice_date and doc.invoice_date.strftime('%-d %B %Y') or '-'"/>
                                        <span><t t-esc="invoice_date"/></span>
                                    </td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 35%; border: none;"><strong>No Faktur</strong></td>
                                    <td style="width: 2%; border: none;"><strong>:</strong></td>
                                    <td style="width: 53%; border: none;"><span t-field="doc.no_faktur"/></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 35%; border: none;"><strong>Ditujukan Kepada</strong></td>
                                    <td style="width: 2%; border: none;"><strong>:</strong></td>
                                    <td style="width: 53%; border: none;"><span t-field="doc.partner_id"/></td>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table table-bordered" border="1" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th rowspan="2" style="text-align: center; vertical-align: middle; width: 5%;">NO</th>
                                    <th rowspan="2" style="text-align: center; vertical-align: middle; width: 30%;">DISKRIPSI</th>
                                    <th colspan="2" style="text-align: center;">SATUAN</th>
                                    <th rowspan="2" style="text-align: center; vertical-align: middle; width: 15%;">HARGA</th>
                                    <th rowspan="2" style="text-align: center; vertical-align: middle;">JUMLAH</th>
                                </tr>
                                <tr>
                                    <th style="text-align: center; width: 15%;">Jumlah</th>
                                    <th style="text-align: center; width: 15%;">Jenis</th>
                                </tr>

                            </thead>
                            <tbody>
                                <t t-if="doc.line_ids">
                                <t t-set="index" t-value="0"/>
                                    <t t-foreach="doc.line_ids" t-as="line">
                                    <t t-set="index" t-value="index + 1"/>
                                        <t t-if="line.display_type == 'product'">
                                            <tr>
                                                <td style="text-align: center; vertical-align: middle;"><t t-esc="index"/></td>
                                                <td style="text-align: center; vertical-align: middle;"><t t-esc="line.name"/></td>
                                                <td style="text-align: center; vertical-align: middle;"><t t-esc="line.quantity"/></td>
                                                <td style="text-align: center; vertical-align: middle;"><t t-esc="line.product_uom_id.name"/></td>
                                                <td style="text-align: center; vertical-align: middle;"><t t-esc="line.price_unit"/></td>
                                                <td style="text-align: center; vertical-align: middle;"><t t-esc="'Rp {:,.2f}'.format(line.price_subtotal)"/></td>


                                            </tr>
                                        </t>

                                    </t>
                                </t>
                            </tbody>
                            <tfoot style="border: none;">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px;'"/>

                                <tr t-att-style="style_no_border">
                                    <td colspan="4" t-att-style="style_no_border"></td>
                                    <td style="text-align: right; font-weight: bold; border: none;">Sub Total:</td>
                                    <td style="text-align: right; border: none;"><t t-esc="doc.amount_untaxed"/></td>
                                </tr>
                                <tr t-att-style="style_no_border" t-if="doc.amount_tax">
                                    <td colspan="4" t-att-style="style_no_border"></td>
                                    <td style="text-align: right; font-weight: bold; border: none;">
                                        <t t-esc="doc.line_ids.tax_ids.name"/>:
                                    </td>
                                    <td style="text-align: right; border: none;">
                                        <t t-esc="doc.amount_tax"/>
                                    </td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td colspan="4" t-att-style="style_no_border"></td>
                                    <td style="text-align: right; font-weight: bold; border: none;">Total:</td>
                                    <td style="text-align: right; border: none;"><t t-esc="'Rp {:,.2f}'.format(doc.amount_total)"/></td>
                                </tr>


                            </tfoot>
                        </table>

                        <br/><br/><br/>

                        <table style="width: 100%; border: none; ">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 50%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <t t-esc="doc.company_id.name"/>
                                    </th>
                                </tr>
                            </tbody>
                        </table>

                        <br/><br/><br/><br/><br/>

                        <table style="width: 100%; border: none; ">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <th style="width: 75%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center; text-decoration: underline;">
                                        <t t-esc="doc.approver and str(doc.approver).upper() or ''"/>
                                    </th>
                                </tr>
                            </tbody>

                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 75%; padding: 5px; border: none; text-align: center;">
                                        <strong><t/></strong>
                                    </td>
                                    <td style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <strong><t t-esc="doc.approver_role and str(doc.approver_role).capitalize() or ''"/></strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>


                    <!-- <p style="page-break-before:auto;"> </p> -->
                    <p style="page-break-before:always;"> </p>



                    <div class="page">
                        <h2 style="text-align: center; text-decoration: underline;">KWITANSI</h2>
                        <table class="table" style="border: none; width: 100%; border-collapse: collapse;">
                            <tbody style="border: none;">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 20%; border: none;"><strong>No.</strong></td>
                                    <td style="width: 2%; border: none;"><strong></strong></td>
                                    <td style="width: 78%; border: none;"></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 20%; border: none;"><strong><span t-field="doc.name"/></strong></td>
                                    <td style="width: 2%; border: none;"><strong></strong></td>
                                    <td style="width: 78%; border: none;"></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 20%; border: none;"><strong></strong></td>
                                    <td style="width: 2%; border: none;"><strong></strong></td>
                                    <td style="width: 78%; border: none;"></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 20%; border: none;"><strong>Telah terima dari</strong></td>
                                    <td style="width: 2%; border: none;"><strong>:</strong></td>
                                    <td style="width: 78%; border: none;  border-bottom: 1px solid black;"><span t-field="doc.partner_id"/></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 20%; border: none;"><strong>Uang sejumlah</strong></td>
                                    <td style="width: 2%; border: none;"><strong>:</strong></td>
                                    <td style="width: 78%; border: none; border-bottom: 1px solid black;"><span t-esc="doc.amount_to_text_id(doc.amount_total)"/></td>
                                </tr>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 20%; border: none;"><strong>Untuk pembayaran</strong></td>
                                    <td style="width: 2%; border: none;"><strong>:</strong></td>
                                    <td style="width: 78%; border: none; border-bottom: 1px solid black;">
                                        <t t-foreach="doc.line_ids" t-as="line">
                                            <t t-if="line.display_type == 'product'">
                                                <span t-esc="line.product_id.name"/>
                                            </t>
                                        </t>
                                    </td>
                                </tr>
                            </tbody>
                        </table>


                        <br/><br/><br/>


                        <table style="width: 100%; border: none; ">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <th style="width: 35%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 40%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <t t-set="invoice_date" t-value="doc.invoice_date and doc.invoice_date.strftime('%-d %B %Y') or '-'"/>
                                        <t t-set="company_street" t-value="doc.company_id.city"/>
                                        <span><t t-esc="company_street"/>, <t t-esc="invoice_date"/></span>
                                    </th>
                                </tr>
                            </tbody>

                            <tfoot style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 35%; padding: 25px 10px; border-top: 1px solid black; border-bottom: 1px solid black; text-align: center; border-left: none; border-right: none;">
                                        <strong style="font-size: 25px;"><t t-esc="'Rp {:,.2f}'.format(doc.amount_total)"/></strong>
                                    </td>
                                    <td style="width: 40%; padding: 5px; border: none; text-align: center;">
                                        <strong><t/></strong>
                                    </td>
                                    <td style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <strong></strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>

                        <br/><br/><br/>

                        <table style="width: 100%; border: none; ">
                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <th style="width: 75%; padding: 5px; border: none; text-align: center;">
                                        <t/>
                                    </th>
                                    <th style="width: 25%; padding: 5px; border: none; text-align: center; text-decoration: underline;">
                                        <t t-esc="doc.approver and str(doc.approver).upper() or ''"/>
                                    </th>
                                </tr>
                            </tbody>

                            <tbody style="border: none">
                                <t t-set="style_no_border" t-value="'border: none !important; padding: 5px; text-align: center;'"/>
                                <tr t-att-style="style_no_border">
                                    <td style="width: 75%; padding: 5px; border: none; text-align: center;">
                                        <strong><t/></strong>
                                    </td>
                                    <td style="width: 25%; padding: 5px; border: none; text-align: center;">
                                        <strong><t t-esc="doc.approver_role and str(doc.approver_role).capitalize() or ''"/></strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                 </t>

            </t>
        </t>
    </template>

    <record id="action_report_invoice_custom" model="ir.actions.report">
        <field name="name">Invoice Kwintasi</field>
        <field name="model">account.move</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ap_reporting.report_invoice_custom</field>
        <field name="report_file">ap_reporting.report_invoice_custom</field>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="binding_type">report</field>
    </record>

</odoo>