from odoo import fields, models, api
from odoo.exceptions import UserError, ValidationError


class MemoSettlement(models.Model):
    _name = 'memo.settlement'
    _inherit = ["analytic.mixin"]
    _description = 'Memo Settlement'


    name = fields.Char('Number')
    date = fields.Date('Date', default=fields.Date.today())
    memo_id = fields.Many2one('memo.memo', 'Memo')
    journal_id = fields.Many2one('account.journal', 'Journal')
    state = fields.Selection(selection=[('draft', 'Draft'), ('posted', 'Posted')], default='draft', string='State')
    journal_ids = fields.Many2many('account.move.line', string='Journal Line')
    memo_move_id = fields.Many2one('account.move', string='Journal Memo')
    analytic_distribution = fields.Json(string='Analytic Distribution')

    bill_ids = fields.One2many('memo.settlement.line', 'memo_settlement_id', string='Bills')

    @api.model
    def create(self, vals):
        res = super(MemoSettlement, self).create(vals)
        res.name = self.env['ir.sequence'].next_by_code('account.move.settlement')
        return res

    def default_get(self, fields_list):
        defaults = super().default_get(fields_list)
        journal_id = self.env['account.journal'].search([('code', '=', 'SET')], limit=1)
        if journal_id:
            defaults['journal_id'] = journal_id.id
        return defaults

    @api.onchange('memo_id')
    def onchange_memo(self):
        if self.memo_id:
            bills = self.env['account.move'].search([('move_type', '=', 'in_invoice'),
                                                     ('memo_id', '=', self.memo_id.id),
                                                     ('state', '=', 'posted')])
            datas = []
            self.bill_ids = [(5, 0, 0)]
            for bill in bills:
                label = False
                data_label = bill.invoice_line_ids.filtered(lambda x: x.name)
                if data_label:
                    label = data_label[0].name

                data = (0, 0, {
                    'move_id': bill.id,
                    'currency_id': bill.currency_id.id,
                    'date': bill.date,
                    'partner_id': bill.partner_id.id,
                    'ref': bill.ref,
                    'label': label,
                    'journal_id': bill.journal_id.id,
                    'company_id': bill.company_id.id,
                    'amount_total_signed': bill.amount_total_signed
                })
                datas.append(data)
            self.bill_ids = datas
        else:
            self.bill_ids = [(5, 0, 0)]

    def unlink(self):
        for req in self:
            if req.state == 'posted':
                raise ValidationError('Cannot delete posted data!')

            req.bill_ids.unlink()

        return super().unlink()

    @api.constrains('bill_ids')
    def check_line(self):
        for rec in self:
            for bill in rec.bill_ids:
                if not bill.move_id:
                    raise ValidationError('Required Bill!')

                if not bill.account_expense_id:
                    raise ValidationError('Required Account Expense on Number %s!'%(bill.move_id.name))

    def action_posted(self):
        credit = 0
        currency_id = False
        line_ids = []

        for bill in self.bill_ids:
            check_bill = self.env['memo.settlement.line'].search([('move_id', '=', bill.move_id.id),
                                                                  ('memo_settlement_id', '!=', self.id)],
                                                                  limit=1)
            if check_bill:
                raise UserError('Bill number %s already assigned to another settlement (%s)'%(bill.move_id.name, check_bill.memo_settlement_id.name))

            if bill.amount_total_signed != 0:
                amount = bill.amount_total_signed * (-1) if bill.amount_total_signed < 0 else bill.amount_total_signed
                currency_id = bill.currency_id.id

                credit += amount
                vals = {
                    'account_id': bill.account_expense_id.id,
                    'journal_id': self.journal_id.id,
                    'analytic_distribution': self.analytic_distribution,
                    'name': self.name,
                    'currency_id': bill.currency_id.id,
                    'debit': amount,
                    'credit': 0
                }
                line_ids.append((0, 0, vals))

        if credit != 0:
            bdd_account = self.env['account.account'].search([('name', '=', 'Biaya Dibayar Dimuka')], limit=1)
            if not bdd_account:
                raise ValidationError('Cannot found account Biaya Dibayar Dimuka!')

            vals = {
                'account_id': bdd_account.id,
                'journal_id': self.journal_id.id,
                'analytic_distribution': self.analytic_distribution,
                'name': self.name,
                'currency_id': currency_id,
                'debit': 0,
                'credit': credit
            }
            line_ids.append((0, 0, vals))

        data_move = {
            'name': self.name,
            'journal_id': self.journal_id.id,
            'date': self.date,
            'memo_id': self.memo_id.id,
            'memo_settlement_id': self.id,
            'line_ids': line_ids
        }
        move = self.env['account.move'].create(data_move)
        move.action_post()
        self.memo_move_id = move.id
        self.state = 'posted'

        data_journal = []
        for line in move.line_ids:
            data_journal.append(line.id)

        self.journal_ids = [(6, 0, data_journal)]


class MemoSettlementLine(models.Model):
    _name = 'memo.settlement.line'
    _description = 'Memo Settlement Line'


    memo_settlement_id = fields.Many2one('memo.settlement', string='Memo Settlement')
    move_id = fields.Many2one('account.move', 'Number')
    date = fields.Date(string='Date')
    partner_id = fields.Many2one('res.partner', string='Partner')
    ref = fields.Char(string='Reference')
    label = fields.Char(string='Label')
    journal_id = fields.Many2one('account.journal', string='Journal')
    company_id = fields.Many2one('res.company', string='Company')
    amount_total_signed = fields.Monetary(string='Total', currency_field='currency_id')
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id.id)
    account_expense_id = fields.Many2one(
        'account.account',
        string='Account',
        domain=[('account_type', '=', 'expense_direct_cost')]
    )
    state = fields.Selection(
        selection=[('draft', 'Draft'), ('posted', 'Posted')],
        default='draft',
        string='State',
        related='memo_settlement_id.state'
    )
