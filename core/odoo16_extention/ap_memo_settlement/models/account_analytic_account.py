from odoo import fields, models, api
from odoo.exceptions import UserError, ValidationError


class AccountAnalyticAccount(models.Model):
    _inherit = 'account.analytic.account'

    contract_start_date = fields.Date('Contract Start Date')
    contract_end_date = fields.Date('Contract End Date')

    # Keep the old field for backward compatibility (computed from start date)
    contract_date = fields.Date('Contract Date', compute='_compute_contract_date', store=True)

    @api.depends('contract_start_date')
    def _compute_contract_date(self):
        """Compute contract_date from contract_start_date for backward compatibility"""
        for record in self:
            record.contract_date = record.contract_start_date

    @api.constrains('contract_start_date', 'contract_end_date', 'plan_id')
    def _check_contract_dates(self):
        for record in self:
            if record.plan_id and record.plan_id.name == 'Project':
                if not record.contract_start_date:
                    raise ValidationError('Required Contract Start Date!')
                if not record.contract_end_date:
                    raise ValidationError('Required Contract End Date!')
                if (record.contract_start_date and record.contract_end_date and
                    record.contract_start_date > record.contract_end_date):
                    raise ValidationError('Contract Start Date must be before Contract End Date!')