from odoo import fields, models, api, _


class AccountMove(models.Model):
    _name = "account.move"
    _inherit = ["account.move", "analytic.mixin"]

    memo_settlement_id = fields.Many2one('memo.settlement', string='Memo Settlment ID')
    contract_date = fields.Date('Contract Date', compute='_compute_contract', store=True)
    contract_start_date = fields.Date(
        'Contract Start Date', compute='_compute_contract', store=True)
    contract_end_date = fields.Date(
        'Contract End Date', compute='_compute_contract', store=True)
    analytic_distribution = fields.Json(string='Analytic Distribution')

    def _must_check_constrains_date_sequence(self):
        return False

    @api.depends('name', 'journal_id')
    def _compute_made_sequence_hole(self):
        for move in self:
            move.made_sequence_hole = False

    @api.depends('posted_before', 'state', 'journal_id', 'date')
    def _compute_name(self):
        self = self.sorted(lambda m: (m.date, m.ref or '', m.id))

        for move in self:
            if move.state == 'cancel':
                continue

            move_has_name = move.name and move.name != '/'
            if move_has_name or move.state != 'posted':
                if not move.posted_before and not move._sequence_matches_date():
                    if move._get_last_sequence(lock=False):
                        # The name does not match the date and the move is not the first in the period:
                        # Reset to draft
                        move.name = False
                        continue
                else:
                    if move_has_name and move.posted_before or not move_has_name and move._get_last_sequence(lock=False):
                        # The move either
                        # - has a name and was posted before, or
                        # - doesn't have a name, but is not the first in the period
                        # so we don't recompute the name
                        continue
            if move.date and (not move_has_name or not move._sequence_matches_date()):
                if move.move_type == 'out_invoice':
                    sequence = self.env['ir.sequence'].next_by_code('customer.invoice.sequence')
                    parts = sequence.split('|')
                    customer_sequence = f"{parts[1]}/{parts[0]}"

                    move.name = customer_sequence
                else:
                    move._set_next_sequence()

        self.filtered(lambda m: not m.name and not move.quick_edit_mode).name = '/'
        self._inverse_name()

    @api.depends('contract_date', 'analytic_distribution')
    def _compute_contract(self):
        for rec in self:
            # compute contract dates
            if rec.analytic_distribution:
                start_date = False
                end_date = False
                for key in rec.analytic_distribution.keys():
                    check_analytic = self.env['account.analytic.account'].browse(int(key))
                    if check_analytic.contract_start_date:
                        start_date = check_analytic.contract_start_date
                    if check_analytic.contract_end_date:
                        end_date = check_analytic.contract_end_date

                rec.contract_date = start_date  # For backward compatibility
                rec.contract_start_date = start_date
                rec.contract_end_date = end_date
            else:
                rec.contract_date = False
                rec.contract_start_date = False
                rec.contract_end_date = False


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    analytic_distribution = fields.Json(
        inverse="_inverse_analytic_distribution",
        compute='_compute_contract',
        store=True,
        readonly=False
    )

    @api.depends('move_id.analytic_distribution', 'product_id', 'name', 'account_id', 'quantity',
                 'product_uom_id', 'price_unit', 'price_subtotal')
    def _compute_contract(self):
        for rec in self:
            rec.analytic_distribution = rec.move_id.analytic_distribution
