<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_move_inherit_memo_settlement" model="ir.ui.view">
        <field name="name">account.move.inherit.memo.settlement</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="ap_memo.account_move_inherit_memo"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='memo_id']" position="after">
                <label for="analytic_distribution" string="No. Contract" />
                <field name="analytic_distribution" nolabel="1" widget="analytic_distribution" attrs="{'readonly': [('state', '!=', 'draft')]}" />

                <label for="contract_date" string="Contract Date" attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>
                <field name="contract_date" nolabel="1" readonly='1' attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}" />

                <label for="contract_start_date" string="Contract Start Date" attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>
                <field name="contract_start_date" nolabel="1" readonly='1' attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}" />

                <label for="contract_end_date" string="Contract End Date" attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>
                <field name="contract_end_date" nolabel="1" readonly='1' attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}" />

                <label for="memo_settlement_id" string="Memo" invisible='1' />
                <field name="memo_settlement_id" nolabel="1" attrs="{'readonly': [('state', '!=', 'draft')]}" invisible='1' />
            </xpath>
        </field>
    </record>

</odoo>
