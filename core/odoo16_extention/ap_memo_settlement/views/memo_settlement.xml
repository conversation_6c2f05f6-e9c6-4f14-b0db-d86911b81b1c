<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="memo_settlement_view_tree" model="ir.ui.view">
        <field name="name">memo.settlement.view.tree</field>
        <field name="model">memo.settlement</field>
        <field name="arch" type="xml">
            <tree string="Memo Settlement Tree">
                <field name="name" />
                <field name="date" />
                <field name="memo_id" />
                <field name="journal_id" />
                <field name="state" />
            </tree>
        </field>
    </record>

    <record id="memo_settlement_view_form" model="ir.ui.view">
        <field name="name">memo.settlement.view.form</field>
        <field name="model">memo.settlement</field>
        <field name="arch" type="xml">
            <form string="Memo Settlement Form">
                <header>
                    <field name="state" widget="statusbar" readonly='1' />
                    <button string="Posted" name="action_posted" attrs="{'invisible': [('state', '!=', 'draft')]}" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly='1' />
                        </h1>
                    </div>
                    <group>
                        <field name="date" attrs="{'readonly': [('state', '!=', 'draft')], 'required': True}" />
                        <field name="memo_id" required='1' attrs="{'readonly': [('state', '!=', 'draft')], 'required': True}" />
                        <field name="journal_id" required='1' />
                        <field name="memo_move_id" readonly='1' />
                        <field name="analytic_distribution" string='No. Contract' required='1' widget="analytic_distribution" attrs="{'readonly': [('state', '!=', 'draft')]}" />
                    </group>
                    <notebook>
                        <page string="Details">
                            <field name="bill_ids" nolabel='1' attrs="{'readonly': [('state', '!=', 'draft')]}">
                                <tree editable='bottom' >
                                    <field name="memo_settlement_id" invisible='1' />
                                    <field name="move_id" readonly='1' force_save='1' required='1' />
                                    <field name="date" readonly='1' force_save='1' />
                                    <field name="partner_id" readonly='1' force_save='1' />
                                    <field name="ref" readonly='1' force_save='1' />
                                    <field name="label" readonly='1' force_save='1' />
                                    <field name="journal_id" readonly='1' force_save='1' />
                                    <field name="company_id" readonly='1' force_save='1' />
                                    <field name="amount_total_signed" readonly='1' force_save='1' />
                                    <field name="account_expense_id" domain="[('account_type','in', ['expense_direct_cost', 'expense'])]" attrs="{'readonly': [('state', '!=', 'draft')], 'required': True}" />
                                    <field name="no_faktur" attrs="{'readonly': [('state', '!=', 'draft')]}" />
                                    <field name="state" invisible='1' />
                                    <field name="currency_id" invisible='1' />
                                </tree>
                                <form >
                                    <group>
                                        <field name="memo_settlement_id" invisible='1' />
                                        <field name="move_id" readonly='1' force_save='1' required='1' />
                                        <field name="date" readonly='1' force_save='1' />
                                        <field name="partner_id" readonly='1' force_save='1' />
                                        <field name="ref" readonly='1' force_save='1' />
                                        <field name="label" readonly='1' force_save='1' />
                                        <field name="journal_id" readonly='1' force_save='1' />
                                        <field name="company_id" readonly='1' force_save='1' />
                                        <field name="amount_total_signed" readonly='1' force_save='1' />
                                        <field name="account_expense_id" domain="[('account_type','in', ['expense_direct_cost', 'expense'])]" attrs="{'readonly': [('state', '!=', 'draft')], 'required': True}" />
                                        <field name="no_faktur" attrs="{'readonly': [('state', '!=', 'draft')]}" />
                                        <field name="state" invisible='1' />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="Journals">
                            <field name="journal_ids" readonly='1' force_save='1' nolabel='1' />
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="memo_settlement_action" model="ir.actions.act_window">
        <field name="name">Memo Settlement</field>
        <field name="res_model">memo.settlement</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem
        id="memo_settlment_menu"
        name="Memo Settlement"
        action="memo_settlement_action"
        parent="account.menu_finance_payables"
        sequence="1"
        />

</odoo>
