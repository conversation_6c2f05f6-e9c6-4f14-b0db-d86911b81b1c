<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Enhanced Report Template for Memo Settlement -->
    <template id="memo_settlement_report_template">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-foreach="docs" t-as="doc">
                    <div class="page" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">

                        <!-- Professional Header -->
                        <div class="row mb-4">
                            <div class="col-12 text-center">
                                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                    <h1 style="margin: 0; font-size: 28px; font-weight: 300; letter-spacing: 1px;">MEMO SETTLEMENT REPORT</h1>
                                    <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;">Financial Settlement Documentation</p>
                                </div>
                            </div>
                        </div>

                        <!-- Settlement Overview Card -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; background-color: #fafafa;">
                                    <h3 style="color: #333; margin-bottom: 20px; font-size: 18px; border-bottom: 2px solid #667eea; padding-bottom: 8px;">
                                        <i class="fa fa-info-circle" style="margin-right: 8px;"></i>Settlement Information
                                    </h3>

                                    <div class="row">
                                        <!-- Left Column -->
                                        <div class="col-6">
                                            <div class="info-item" style="margin-bottom: 12px;">
                                                <span style="font-weight: 600; color: #555; display: inline-block; width: 140px;">Settlement No:</span>
                                                <span style="color: #333; font-weight: 500; background: #e3f2fd; padding: 4px 8px; border-radius: 4px;">
                                                    <t t-esc="doc.name"/>
                                                </span>
                                            </div>
                                            <div class="info-item" style="margin-bottom: 12px;">
                                                <span style="font-weight: 600; color: #555; display: inline-block; width: 140px;">Date:</span>
                                                <span style="color: #333;">
                                                    <t t-esc="doc.date.strftime('%d %B %Y') if doc.date else '-'"/>
                                                </span>
                                            </div>
                                            <div class="info-item" style="margin-bottom: 12px;">
                                                <span style="font-weight: 600; color: #555; display: inline-block; width: 140px;">Memo Reference:</span>
                                                <span style="color: #333;">
                                                    <t t-esc="doc.memo_id.name if doc.memo_id else '-'"/>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Right Column -->
                                        <div class="col-6">
                                            <div class="info-item" style="margin-bottom: 12px;">
                                                <span style="font-weight: 600; color: #555; display: inline-block; width: 120px;">Status:</span>
                                                <span t-if="doc.state == 'draft'" style="background: #fff3cd; color: #856404; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                                                    ● DRAFT
                                                </span>
                                                <span t-if="doc.state == 'posted'" style="background: #d4edda; color: #155724; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                                                    ● POSTED
                                                </span>
                                            </div>
                                            <div class="info-item" style="margin-bottom: 12px;">
                                                <span style="font-weight: 600; color: #555; display: inline-block; width: 120px;">Company:</span>
                                                <span style="color: #333;">
                                                    <t t-esc="doc.company_id.name if doc.company_id else env.company.name"/>
                                                </span>
                                            </div>
                                            <div class="info-item" style="margin-bottom: 12px;">
                                                <span style="font-weight: 600; color: #555; display: inline-block; width: 120px;">Journal:</span>
                                                <span style="color: #333;">
                                                    <t t-esc="doc.journal_id.name if doc.journal_id else '-'"/>
                                                </span>
                                            </div>
                                            <div class="info-item" style="margin-bottom: 12px;" t-if="doc.analytic_distribution">
                                                <span style="font-weight: 600; color: #555; display: inline-block; width: 120px;">Contract:</span>
                                                <span style="color: #333;">
                                                    <t t-foreach="doc.analytic_distribution.items()" t-as="analytic">
                                                        <t t-set="analytic_name" t-value="env['account.analytic.account'].browse(int(analytic[0])).name"/>
                                                        <t t-esc="analytic_name"/>
                                                    </t>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bills Details Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; background-color: white;">
                                    <h3 style="color: #333; margin-bottom: 20px; font-size: 18px; border-bottom: 2px solid #667eea; padding-bottom: 8px;">
                                        <i class="fa fa-list-alt" style="margin-right: 8px;"></i>Settlement Line Items
                                    </h3>

                                    <!-- Enhanced Table -->
                                    <div style="overflow-x: auto;">
                                        <table style="width: 100%; border-collapse: collapse; font-size: 13px;">
                                            <thead>
                                                <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                                    <th style="padding: 12px 8px; text-align: center; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 5%;">#</th>
                                                    <th style="padding: 12px 8px; text-align: left; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 12%;">Bill Number</th>
                                                    <th style="padding: 12px 8px; text-align: center; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 8%;">Date</th>
                                                    <th style="padding: 12px 8px; text-align: left; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 15%;">Vendor</th>
                                                    <th style="padding: 12px 8px; text-align: left; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 10%;">Reference</th>
                                                    <th style="padding: 12px 8px; text-align: left; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 18%;">Description</th>
                                                    <th style="padding: 12px 8px; text-align: left; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 12%;">Account</th>
                                                    <th style="padding: 12px 8px; text-align: center; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 10%;">Invoice No</th>
                                                    <th style="padding: 12px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 10%;">Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-set="line_number" t-value="1"/>
                                                <t t-set="total_amount" t-value="0"/>
                                                <t t-foreach="doc.bill_ids" t-as="line">
                                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                                        <td style="padding: 10px 8px; text-align: center; border: 1px solid #dee2e6; background: #f8f9fa; font-weight: 500;">
                                                            <t t-esc="line_number"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; border: 1px solid #dee2e6; color: #495057; font-weight: 500;">
                                                            <t t-esc="line.move_id.name if line.move_id else '-'"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; text-align: center; border: 1px solid #dee2e6; color: #6c757d;">
                                                            <t t-esc="line.date.strftime('%d/%m/%Y') if line.date else '-'"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; border: 1px solid #dee2e6; color: #495057;">
                                                            <t t-esc="line.partner_id.name if line.partner_id else '-'"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; border: 1px solid #dee2e6; color: #6c757d; font-style: italic;">
                                                            <t t-esc="line.ref if line.ref else '-'"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; border: 1px solid #dee2e6; color: #495057;">
                                                            <t t-esc="line.label if line.label else '-'"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; border: 1px solid #dee2e6; color: #495057; font-size: 11px;">
                                                            <div style="font-weight: 600; color: #007bff;">
                                                                <t t-esc="line.account_expense_id.code if line.account_expense_id else '-'"/>
                                                            </div>
                                                            <div style="color: #6c757d; margin-top: 2px;">
                                                                <t t-esc="line.account_expense_id.name[:25] + '...' if line.account_expense_id and len(line.account_expense_id.name) > 25 else (line.account_expense_id.name if line.account_expense_id else '-')"/>
                                                            </div>
                                                        </td>
                                                        <td style="padding: 10px 8px; text-align: center; border: 1px solid #dee2e6; color: #495057; font-weight: 500;">
                                                            <t t-esc="line.move_id.no_faktur if line.move_id and line.move_id.no_faktur else '-'"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #28a745;">
                                                            <span t-field="line.amount_total_signed" t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                                        </td>
                                                    </tr>
                                                    <t t-set="line_number" t-value="line_number + 1"/>
                                                    <t t-set="total_amount" t-value="total_amount + line.amount_total_signed"/>
                                                </t>
                                            </tbody>
                                            <tfoot>
                                                <tr style="background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%); font-weight: 600;">
                                                    <td colspan="8" style="padding: 15px 8px; text-align: right; border: 1px solid #dee2e6; color: #495057; font-size: 14px;">
                                                        <strong>TOTAL SETTLEMENT AMOUNT:</strong>
                                                    </td>
                                                    <td style="padding: 15px 8px; text-align: right; border: 1px solid #dee2e6; color: #dc3545; font-size: 16px; font-weight: 700;">
                                                        <strong t-esc="'%.2f' % total_amount"/>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Journal Entries Section (if posted) -->
                        <div class="row mb-4" t-if="doc.state == 'posted' and doc.journal_ids">
                            <div class="col-12">
                                <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; background-color: white;">
                                    <h3 style="color: #333; margin-bottom: 20px; font-size: 18px; border-bottom: 2px solid #667eea; padding-bottom: 8px;">
                                        <i class="fa fa-book" style="margin-right: 8px;"></i>Journal Entries
                                        <span style="font-size: 12px; color: #6c757d; font-weight: normal; margin-left: 10px;">
                                            (Entry: <t t-esc="doc.memo_move_id.name if doc.memo_move_id else '-'"/>)
                                        </span>
                                    </h3>

                                    <div style="overflow-x: auto;">
                                        <table style="width: 100%; border-collapse: collapse; font-size: 13px;">
                                            <thead>
                                                <tr style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                                    <th style="padding: 12px 8px; text-align: center; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 8%;">#</th>
                                                    <th style="padding: 12px 8px; text-align: left; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 25%;">Account</th>
                                                    <th style="padding: 12px 8px; text-align: left; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 30%;">Description</th>
                                                    <th style="padding: 12px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 12%;">Debit</th>
                                                    <th style="padding: 12px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 12%;">Credit</th>
                                                    <th style="padding: 12px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #495057; width: 13%;">Balance</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-set="journal_line_number" t-value="1"/>
                                                <t t-set="total_debit" t-value="0"/>
                                                <t t-set="total_credit" t-value="0"/>
                                                <t t-foreach="doc.journal_ids" t-as="journal_line">
                                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                                        <td style="padding: 10px 8px; text-align: center; border: 1px solid #dee2e6; background: #f8f9fa; font-weight: 500;">
                                                            <t t-esc="journal_line_number"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; border: 1px solid #dee2e6; color: #495057; font-size: 11px;">
                                                            <div style="font-weight: 600; color: #007bff;">
                                                                <t t-esc="journal_line.account_id.code if journal_line.account_id else '-'"/>
                                                            </div>
                                                            <div style="color: #6c757d; margin-top: 2px;">
                                                                <t t-esc="journal_line.account_id.name if journal_line.account_id else '-'"/>
                                                            </div>
                                                        </td>
                                                        <td style="padding: 10px 8px; border: 1px solid #dee2e6; color: #495057;">
                                                            <t t-esc="journal_line.name if journal_line.name else '-'"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #28a745;">
                                                            <span t-field="journal_line.debit" t-options="{'widget': 'monetary', 'display_currency': journal_line.currency_id}"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #dc3545;">
                                                            <span t-field="journal_line.credit" t-options="{'widget': 'monetary', 'display_currency': journal_line.currency_id}"/>
                                                        </td>
                                                        <td style="padding: 10px 8px; text-align: right; border: 1px solid #dee2e6; font-weight: 600; color: #6f42c1;">
                                                            <span t-field="journal_line.balance" t-options="{'widget': 'monetary', 'display_currency': journal_line.currency_id}"/>
                                                        </td>
                                                    </tr>
                                                    <t t-set="journal_line_number" t-value="journal_line_number + 1"/>
                                                    <t t-set="total_debit" t-value="total_debit + journal_line.debit"/>
                                                    <t t-set="total_credit" t-value="total_credit + journal_line.credit"/>
                                                </t>
                                            </tbody>
                                            <tfoot>
                                                <tr style="background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%); font-weight: 600;">
                                                    <td colspan="3" style="padding: 15px 8px; text-align: right; border: 1px solid #dee2e6; color: #495057; font-size: 14px;">
                                                        <strong>TOTALS:</strong>
                                                    </td>
                                                    <td style="padding: 15px 8px; text-align: right; border: 1px solid #dee2e6; color: #28a745; font-size: 14px; font-weight: 700;">
                                                        <strong t-esc="'%.2f' % total_debit"/>
                                                    </td>
                                                    <td style="padding: 15px 8px; text-align: right; border: 1px solid #dee2e6; color: #dc3545; font-size: 14px; font-weight: 700;">
                                                        <strong t-esc="'%.2f' % total_credit"/>
                                                    </td>
                                                    <td style="padding: 15px 8px; text-align: right; border: 1px solid #dee2e6; color: #6f42c1; font-size: 14px; font-weight: 700;">
                                                        <strong t-esc="'%.2f' % (total_debit - total_credit)"/>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Footer -->
                        <div class="row mt-5">
                            <div class="col-12">
                                <div style="border-top: 2px solid #667eea; padding-top: 20px; margin-top: 30px;">
                                    <div class="row">
                                        <div class="col-6">
                                            <div style="color: #6c757d; font-size: 12px;">
                                                <strong>Report Generated:</strong><br/>
                                                <t t-esc="context_timestamp(datetime.datetime.now()).strftime('%d %B %Y at %H:%M:%S')"/>
                                            </div>
                                        </div>
                                        <div class="col-6 text-right">
                                            <div style="color: #6c757d; font-size: 12px;">
                                                <strong>System:</strong> Odoo ERP<br/>
                                                <strong>Module:</strong> Memo Settlement
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center mt-3">
                                        <p style="color: #adb5bd; font-size: 11px; margin: 0; font-style: italic;">
                                            This document is computer generated and does not require a signature.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- Report Action -->
    <record id="action_memo_settlement_report" model="ir.actions.report">
        <field name="name">Memo Settlement Report</field>
        <field name="model">memo.settlement</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ap_memo_settlement.memo_settlement_report_template</field>
        <field name="report_file">ap_memo_settlement.memo_settlement_report_template</field>
        <field name="binding_model_id" ref="ap_memo_settlement.model_memo_settlement"/>
        <field name="binding_type">report</field>
    </record>

</odoo>
